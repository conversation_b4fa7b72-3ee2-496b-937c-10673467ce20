2025-07-07 16:53:20,980 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-07 16:53:20,980 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-07 16:53:20,981 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-07 16:53:20,982 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-07 16:53:20,982 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-07 16:53:20,983 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-07 16:53:20,983 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-07 16:53:20,983 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-07 16:53:20,984 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-07 16:53:20,984 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-07 16:53:20,985 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-07 16:53:20,985 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-07-07 16:53:20,985 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
2025-07-07 16:53:20,985 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-07-07 16:53:22,923 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
2025-07-07 16:53:22,923 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-07-07 16:53:22,924 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-07-07 16:53:22,924 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-07 16:53:22,926 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-07 16:53:22,926 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-07 16:53:22,978 - AppiumDeviceController - INFO - Successfully imported Airtest library.
